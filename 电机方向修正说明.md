# 电机方向修正说明

## 问题描述
用户反馈硬件连接的小车正方向与代码中的正方向相反，需要调换正反转方向，并要求修改底层代码以确保PID控制不受影响。

## 解决方案
在底层驱动函数`Motor_SetDirc()`中调换了TB6612的IN1/IN2控制逻辑，实现硬件方向修正。

## 修改详情

### 1. 核心修改 - BSP/Src/Motor.c

#### 修改前的控制逻辑：
```c
if (Dirc == DIRC_FOWARD) {
    //正转: IN1=0, IN2=1
    DL_GPIO_clearPins(DIRC_CTRL_PORT, Motor->Motor_IN1_Pin);
    DL_GPIO_setPins(DIRC_CTRL_IN2_PORT, Motor->Motor_IN2_Pin);
}
else if (Dirc == DIRC_BACKWARD) {
    //反转: IN1=1, IN2=0
    DL_GPIO_setPins(DIRC_CTRL_PORT, Motor->Motor_IN1_Pin);
    DL_GPIO_clearPins(DIRC_CTRL_IN2_PORT, Motor->Motor_IN2_Pin);
}
```

#### 修改后的控制逻辑：
```c
if (Dirc == DIRC_FOWARD) {
    //正转: IN1=1, IN2=0 (硬件方向调换)
    DL_GPIO_setPins(DIRC_CTRL_PORT, Motor->Motor_IN1_Pin);
    DL_GPIO_clearPins(DIRC_CTRL_IN2_PORT, Motor->Motor_IN2_Pin);
}
else if (Dirc == DIRC_BACKWARD) {
    //反转: IN1=0, IN2=1 (硬件方向调换)
    DL_GPIO_clearPins(DIRC_CTRL_PORT, Motor->Motor_IN1_Pin);
    DL_GPIO_setPins(DIRC_CTRL_IN2_PORT, Motor->Motor_IN2_Pin);
}
```

### 2. 文档更新

#### 更新的文档：
- `motor.md` - 更新了TB6612控制逻辑说明
- `TB6612_Migration_Summary.md` - 添加了方向调换说明
- `电机方向修正说明.md` - 新增此说明文档

## 修改优势

### 1. 底层修改的好处
- ✅ **上层接口不变**: 所有应用层代码无需修改
- ✅ **PID控制不受影响**: PID参数和逻辑完全不变
- ✅ **循迹功能正常**: 差速转向算法无需调整
- ✅ **编码器反馈正确**: 速度反馈方向自动匹配

### 2. 兼容性保证
- ✅ `Motor_SetDuty(motor, 50.0f)` - 仍然表示正转50%
- ✅ `Motor_SetDuty(motor, -30.0f)` - 仍然表示反转30%
- ✅ PID目标速度设置不变
- ✅ 循迹转向逻辑不变

## 影响范围分析

### 不受影响的功能：
1. **PID速度控制** - 目标速度和实际速度的符号关系保持一致
2. **循迹转向控制** - 差速算法中的左右轮速度调整逻辑不变
3. **编码器反馈** - `Motor_GetSpeed()`函数中的方向判断逻辑自动适配
4. **上层应用** - 所有Task_App.c中的电机控制代码无需修改

### 修改生效的场景：
1. **直接电机控制** - `Motor_SetDuty()`函数的正负值对应的实际转向已调换
2. **方向设置** - `DIRC_FOWARD`和`DIRC_BACKWARD`对应的实际转向已调换
3. **硬件测试** - `Motor_Test_TB6612()`测试函数的转向已调换

## 验证方法

### 1. 基础功能测试
```c
// 测试正转 - 小车应该向前移动
Motor_SetDuty(&Motor_Font_Left, 30.0f);
Motor_SetDuty(&Motor_Font_Right, 30.0f);
Motor_SetDuty(&Motor_Back_Left, 30.0f);
Motor_SetDuty(&Motor_Back_Right, 30.0f);
```

### 2. 循迹功能测试
- 将小车放在黑线上
- 启动循迹程序
- 观察小车是否能正确跟随黑线行驶

### 3. 转向测试
```c
// 测试左转 - 小车应该向左转
Motor_SetDuty(&Motor_Font_Left, 10.0f);   // 左轮慢
Motor_SetDuty(&Motor_Font_Right, 30.0f);  // 右轮快
Motor_SetDuty(&Motor_Back_Left, 10.0f);
Motor_SetDuty(&Motor_Back_Right, 30.0f);
```

## 技术细节

### TB6612控制逻辑对照表

| 功能 | 修改前 | 修改后 | 说明 |
|------|--------|--------|------|
| 正转 | IN1=0, IN2=1 | IN1=1, IN2=0 | 硬件方向调换 |
| 反转 | IN1=1, IN2=0 | IN1=0, IN2=1 | 硬件方向调换 |
| 制动 | IN1=0, IN2=0 | IN1=0, IN2=0 | 保持不变 |
| 停止 | IN1=1, IN2=1 | IN1=1, IN2=1 | 保持不变 |

### 编码器方向适配
`Motor_GetSpeed()`函数中的方向判断逻辑会自动适配新的控制逻辑：
```c
// 根据电机方向调整速度符号
if (Motor->Motor_Dirc == DIRC_BACKWARD && Speed > 0) {
    Speed = -Speed; // 反转时速度为负
}
else if (Motor->Motor_Dirc == DIRC_FOWARD && Speed < 0) {
    Speed = -Speed; // 正转时速度为正
}
```

## 总结

通过在底层驱动中调换IN1/IN2的控制逻辑，成功解决了硬件方向与软件预期相反的问题。这种修改方式：

1. **最小化影响** - 只修改了一个底层函数
2. **保持兼容性** - 上层所有代码无需修改
3. **确保正确性** - PID控制和循迹功能完全不受影响
4. **易于维护** - 修改集中在一处，便于后续维护

修改完成后，小车的正方向将与代码中的正方向保持一致，循迹和PID控制功能将正常工作。
